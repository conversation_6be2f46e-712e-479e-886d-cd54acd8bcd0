import {
  <PERSON>,
  But<PERSON>,
  Text,
  VStack,
  Flex,
  Input,
  NativeSelect,
} from "@chakra-ui/react"
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"
import { createFileRoute, useNavigate } from "@tanstack/react-router"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import React from "react"
import { QuestionnairesService, type QuestionnaireStartRequest } from "@/client"
import Header from "@/components/header"
import { ChineseText } from "@/components/ui/fonts"
export const Route = createFileRoute("/_layout/questionnaire-start")({
  component: QuestionnaireStartPage,
})

const questionnaireStartSchema = z.object({
  name: z.string().min(1, "姓名為必填項目"),
  birth_info: z.string().optional(),
  caregiver: z.string().optional(),
  feeding: z.string().optional(),
  native_language: z.string().optional(),
  grade_level: z.string().optional(),
  survey_type_id: z.number().min(1, "請選擇評估類型"),
})

type QuestionnaireStartData = z.infer<typeof questionnaireStartSchema>

// Helper function to convert survey type names to Chinese age ranges
const getSurveyTypeDisplayName = (surveyTypeName: string): string => {
  const nameMapping: Record<string, string> = {
    "3-6 years questionnaire": "3-6歲",
    "7-11 years questionnaire": "7-11歲",
    "12-15 years questionnaire": "12-15歲"
  }

  return nameMapping[surveyTypeName] || surveyTypeName
}

function QuestionnaireStartPage() {
  const navigate = useNavigate()
  const queryClient = useQueryClient()

  // Get available questionnaire for current client
  const { data: questionnaire, isLoading: questionnaireLoading } = useQuery({
    queryKey: ["my-questionnaire"],
    queryFn: () => QuestionnairesService.getMyAvailableQuestionnaire(),
    enabled: false, // Temporarily disable to test page rendering
  })

  // Get survey types
  const { data: surveyTypes, isLoading: surveyTypesLoading } = useQuery({
    queryKey: ["public-survey-types"],
    queryFn: () => QuestionnairesService.getPublicSurveyTypes(),
  })

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
  } = useForm<QuestionnaireStartData>({
    resolver: zodResolver(questionnaireStartSchema),
  })

  const startQuestionnaireMutation = useMutation({
    mutationFn: (data: QuestionnaireStartRequest) =>
      QuestionnairesService.startMyQuestionnaire({ requestBody: data }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["my-questionnaire"] })
      navigate({ to: "/questionnaire-questions" })
    },
    onError: (error: any) => {
      console.error("Failed to start questionnaire:", error)
      alert("開始評估失敗: " + (error?.message || "未知錯誤"))
    },
  })

  const onSubmit = (data: QuestionnaireStartData) => {
    const startData: QuestionnaireStartRequest = {
      ...data,
      survey_type_id: data.survey_type_id,
    }

    startQuestionnaireMutation.mutate(startData)
  }

  // Debug: Show loading and data states
  console.log("Questionnaire loading:", questionnaireLoading)
  console.log("Survey types loading:", surveyTypesLoading)
  console.log("Questionnaire data:", questionnaire)
  console.log("Survey types data:", surveyTypes)
  console.log("Survey types array?", Array.isArray(surveyTypes))
  console.log("Survey types length:", surveyTypes?.length)

  // Set default survey type when data loads
  React.useEffect(() => {
    if (surveyTypes && surveyTypes.length > 1) {
      console.log("Setting default survey type to:", surveyTypes[0])
      reset({
        survey_type_id: surveyTypes[0].survey_type_id
      })
    }
  }, [surveyTypes, reset])

  if (questionnaireLoading || surveyTypesLoading) {
    return (
      <Box maxW="4xl" py={8} mx="auto">
        <VStack gap={4}>
          <Text>載入中...</Text>
          <Text fontSize="sm" color="gray.500">
            Questionnaire loading: {questionnaireLoading ? "Yes" : "No"}
          </Text>
          <Text fontSize="sm" color="gray.500">
            Survey types loading: {surveyTypesLoading ? "Yes" : "No"}
          </Text>
        </VStack>
      </Box>
    )
  }

  // For now, let's show the page even without questionnaire data for testing
  // if (!questionnaire) {
  //   return (
  //     <Container maxW="4xl" py={8}>
  //       <VStack gap={6} textAlign="center">
  //         <Heading size="lg" color="red.500">
  //           <ChineseText>沒有可用的評估</ChineseText>
  //         </Heading>
  //         <ChineseText color="gray.600">
  //           您目前沒有可用的評估問卷，請聯繫客服人員。
  //         </ChineseText>
  //         <Button onClick={() => navigate({ to: "/" })}>
  //           <ChineseText>回到首頁</ChineseText>
  //         </Button>
  //       </VStack>
  //     </Container>
  //   )
  // }

  const termsSection = () => {
    return (
      <Flex
        width='full'
        justify={'center'}
        mt={'10'}
      >
        <Flex
          as="nav"
          bgColor={'white'}
          direction={'column'}
          w='90vw'
        >
          <Flex
            direction={'column'}
            m={'5'}
          >

            <ChineseText color={'black'} mb={2}>1. 評估目的與範圍</ChineseText>
            <ChineseText color={'black'} mb={2} fontWeight="normal">
              1. 本評估旨在透過標準化工具與專業方法，了解兒童在認知、語言、邏輯推理等核心智能領域的發展狀況，為教育或介入方案提供參考。
            </ChineseText>
            <ChineseText color={'black'} mb={2} fontWeight="normal">
              2. 評估內容及流程已通過倫理審查，不包含任何可能對兒童身心造成傷害的環節。
            </ChineseText>
            <ChineseText color={'black'} mb={4}>&nbsp;</ChineseText>

            <ChineseText color={'black'} mb={2}>2. 監護人知情同意</ChineseText>
            <ChineseText color={'black'} mb={2} fontWeight="normal">
              1. 監護人有權知悉評估的具體內容、方法、預期時長及潛在風險，並可透過書面或口頭向評估機構提出詢問。
            </ChineseText>
            <ChineseText color={'black'} mb={2} fontWeight="normal">
              2. 監護人需確保所提供兒童的健康狀況、教育背景等資訊真實有效，否則可能影響評估結果的準確性。
            </ChineseText>
            <ChineseText color={'black'} mb={4}>&nbsp;</ChineseText>

            <ChineseText color={'black'} mb={2}>3. 隱私與資料保護</ChineseText>
            <ChineseText color={'black'} mb={2} fontWeight="normal">
              1. 評估過程中所取得的所有個人資訊（含測試結果、影像記錄等）將嚴格保密，未經監護人書面授權，不得向第三方揭露。
            </ChineseText>
            <ChineseText color={'black'} mb={2} fontWeight="normal">
              2. 資料儲存符合《個人資訊保護法》要求，評估報告僅用於約定用途，銷毀週期為____年（具體時間需明確）。
            </ChineseText>
            <ChineseText color={'black'} mb={4}>&nbsp;</ChineseText>

            <ChineseText color={'black'} mb={2}>4. 責任限制</ChineseText>
            <ChineseText color={'black'} mb={2} fontWeight="normal">
              1. 評估結果基於兒童當次表現及現有科學工具得出，受環境、情緒等因素影響，**不作為唯一診斷或法律依據**。
            </ChineseText>
            <ChineseText color={'black'} fontWeight="normal">
              2. 評估機構不承擔因監護人誤用、曲解報告內容所導致的直接或間接責任。
            </ChineseText>

          </Flex>

        </Flex>


      </Flex >
    )
  }

  const inputWidth = { base: 'full', md: '50%' }

  return (
    <Box bg="#f9f5e9" minH="100vh" position="relative">
      <Header title="智能核心評估" />

      {/* Terms and Conditions Section */}
      {termsSection()}



      {/* Personal Information Form */}
      {(
        <Box
          w='full'
          mt='20'
          justifyItems={'center'}
        >
          <form onSubmit={handleSubmit(onSubmit)}>
            <VStack gap={6} align="stretch" w='90vw'>
              {/* Name Field */}
              <Box>
                <Text
                  fontFamily="Roboto"
                  fontSize="16px"
                  color="#0c1421"
                  mb={2}
                  fontWeight="normal"
                >
                  孩子姓名
                </Text>
                <Input
                  w={inputWidth}
                  {...register("name")}
                  placeholder="請輸入孩子姓名"
                  bg="#f7fbff"
                  border="1px solid #d4d7e3"
                  borderRadius="12px"
                  h="50px"
                  fontSize="16px"
                  color="#2d3748"
                  _placeholder={{ color: "#2d3748" }}
                />
                {errors.name && (
                  <Text color="red.500" fontSize="14px" mt={1}>
                    {errors.name.message}
                  </Text>
                )}
              </Box>
              {/* Survey Type Field */}
              <Box>
                <Text
                  fontFamily="Roboto"
                  fontSize="16px"
                  color="#0c1421"
                  mb={2}
                  fontWeight="normal"
                >
                  歲數
                </Text>
                <NativeSelect.Root w={inputWidth}>
                  <NativeSelect.Field
                    {...register("survey_type_id", {
                      setValueAs: (value) => value ? parseInt(value, 10) : undefined
                    })}
                    bg="#f7fbff"
                    border="1px solid #d4d7e3"
                    borderRadius="12px"
                    h="50px"
                    fontSize="16px"
                    color="#2d3748"
                    _placeholder={{ color: "#2d3748" }}
                  >
                    
                    {surveyTypes?.map((surveyType) => (
                      <option key={surveyType.survey_type_id} value={surveyType.survey_type_id}>
                        {getSurveyTypeDisplayName(surveyType.name)}
                      </option>
                    ))}
                  </NativeSelect.Field>
                  <NativeSelect.Indicator />
                </NativeSelect.Root>
                {errors.survey_type_id && (
                  <Text color="red.500" fontSize="14px" mt={1}>
                    {errors.survey_type_id.message}
                  </Text>
                )}
              </Box>

              {/* Birth Info Field */}
              <Box>
                <Text
                  fontFamily="Roboto"
                  fontSize="16px"
                  color="#0c1421"
                  mb={2}
                  fontWeight="normal"
                >
                  孩子出生方式
                </Text>
                <NativeSelect.Root w={inputWidth}>
                  <NativeSelect.Field
                    {...register("birth_info")}
                    bg="#f7fbff"
                    border="1px solid #d4d7e3"
                    borderRadius="12px"
                    h="50px"
                    fontSize="16px"
                    color="#2d3748"
                    _placeholder={{ color: "#2d3748" }}
                  >
                    <option value="">順產</option>
                    <option value="順產">順產</option>
                    <option value="剖腹產">剖腹產</option>
                    <option value="其他">其他</option>
                  </NativeSelect.Field>
                  <NativeSelect.Indicator />
                </NativeSelect.Root>
              </Box>

              {/* Caregiver Field */}
              <Box>
                <Text
                  fontFamily="Roboto"
                  fontSize="16px"
                  color="#0c1421"
                  mb={2}
                  fontWeight="normal"
                >
                  孩子多數由誰照顧
                </Text>
                <NativeSelect.Root w={inputWidth}>
                  <NativeSelect.Field
                    {...register("caregiver")}
                    bg="#f7fbff"
                    border="1px solid #d4d7e3"
                    borderRadius="12px"
                    h="50px"
                    fontSize="16px"
                    color="#2d3748"
                    _placeholder={{ color: "#2d3748" }}
                  >
                    <option value="">媽媽</option>
                    <option value="媽媽">媽媽</option>
                    <option value="爸爸">爸爸</option>
                    <option value="祖父母">祖父母</option>
                    <option value="外祖父母">外祖父母</option>
                    <option value="其他">其他</option>
                  </NativeSelect.Field>
                  <NativeSelect.Indicator />
                </NativeSelect.Root>
              </Box>

              {/* Feeding Field */}
              <Box>
                <Text
                  fontFamily="Roboto"
                  fontSize="16px"
                  color="#0c1421"
                  mb={2}
                  fontWeight="normal"
                >
                  孩子喂養
                </Text>
                <NativeSelect.Root w={inputWidth}>
                  <NativeSelect.Field
                    {...register("feeding")}
                    bg="#f7fbff"
                    border="1px solid #d4d7e3"
                    borderRadius="12px"
                    h="50px"
                    fontSize="16px"
                    color="#2d3748"
                    _placeholder={{ color: "#2d3748" }}
                  >
                    <option value="">奶粉</option>
                    <option value="母乳">母乳</option>
                    <option value="奶粉">奶粉</option>
                    <option value="混合餵養">混合餵養</option>
                  </NativeSelect.Field>
                  <NativeSelect.Indicator />
                </NativeSelect.Root>
              </Box>

              {/* Native Language Field */}
              <Box>
                <Text
                  fontFamily="Roboto"
                  fontSize="16px"
                  color="#0c1421"
                  mb={2}
                  fontWeight="normal"
                >
                  母語
                </Text>
                <NativeSelect.Root w={inputWidth}>
                  <NativeSelect.Field
                    {...register("native_language")}
                    bg="#f7fbff"
                    border="1px solid #d4d7e3"
                    borderRadius="12px"
                    h="50px"
                    fontSize="16px"
                    color="#2d3748"
                    _placeholder={{ color: "#2d3748" }}
                  >
                    <option value="">粵語</option>
                    <option value="粵語">粵語</option>
                    <option value="普通話">普通話</option>
                    <option value="英語">英語</option>
                    <option value="其他">其他</option>
                  </NativeSelect.Field>
                  <NativeSelect.Indicator />
                </NativeSelect.Root>
              </Box>

              {/* Grade Level Field */}
              <Box>
                <Text
                  fontFamily="Roboto"
                  fontSize="16px"
                  color="#0c1421"
                  mb={2}
                  fontWeight="normal"
                >
                  就讀年級
                </Text>
                <NativeSelect.Root w={inputWidth}>
                  <NativeSelect.Field
                    {...register("grade_level")}
                    bg="#f7fbff"
                    border="1px solid #d4d7e3"
                    borderRadius="12px"
                    h="50px"
                    fontSize="16px"
                    color="#2d3748"
                    _placeholder={{ color: "#2d3748" }}
                  >
                    <option value="">請選擇年級</option>
                    <option value="幼稚園K1">幼稚園K1</option>
                    <option value="幼稚園K2">幼稚園K2</option>
                    <option value="幼稚園K3">幼稚園K3</option>
                    <option value="小學一年級">小學一年級</option>
                    <option value="小學二年級">小學二年級</option>
                    <option value="小學三年級">小學三年級</option>
                    <option value="小學四年級">小學四年級</option>
                    <option value="小學五年級">小學五年級</option>
                    <option value="小學六年級">小學六年級</option>
                    <option value="中學一年級">中學一年級</option>
                    <option value="中學二年級">中學二年級</option>
                    <option value="中學三年級">中學三年級</option>
                    <option value="其他">其他</option>
                  </NativeSelect.Field>
                  <NativeSelect.Indicator />
                </NativeSelect.Root>
              </Box>

              {/* Submit Button */}
              <Box display="flex" justifyContent="center" mt={8}>
                <Button
                  type="submit"
                  bg="#d3401f"
                  color="#ffffff"
                  borderRadius="6px"
                  h="48px"
                  w="267px"
                  fontSize="18px"
                  fontWeight="600"
                  fontFamily="Inter"
                  loading={isSubmitting || startQuestionnaireMutation.isPending}
                  _hover={{ bg: "#b8351a" }}
                  _active={{ bg: "#a02e17" }}
                >
                  開始評估
                </Button>
              </Box>
            </VStack>
          </form>
        </Box>
      )}
    </Box>
  )
}
